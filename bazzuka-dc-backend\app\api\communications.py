import os
import json
import uuid
from datetime import datetime

from flask import Blueprint, jsonify, request, g

from app.utils.supabase.queries import get_comm_logs_by_defaulter_id

from app.core.defaulters import defaulters
from app.core.vapi import get_call_transcripts
from app.utils.supabase.queries import (
    get_comm_logs,
    get_emails_by_id,
    get_comm_by_id,
    get_most_recent_action_item_for_issue,
    update_comm_log,
    get_calllog_by_id,
    insert_comm_logs,
)

communications_bp = Blueprint("commmunications", __name__)


@communications_bp.route("", methods=["GET", "OPTIONS", "POST"])
def get_communications():
    if request.method == "OPTIONS":
        return jsonify({}), 204

    if os.environ.get("FLASK_DEBUG"):
        org_id = "8241d390-a8b5-4f59-b0a9-b95c074db3f5"
    else:
        try:
            org_id = g.user.user_metadata.get("org_id")
        except Exception as e:
            return jsonify({}), 500

    if request.method == "GET":
        # get the limit and offset from the query string
        limit = min(int(request.args.get("limit", 10)), 1000)
        offset = int(request.args.get("offset", 0))

        comm_logs = get_comm_logs(limit=limit, offset=offset)

        # Extract unique defaulter_ids from comm_logs
        defaulter_ids = {comm_log["defaulter_id"] for comm_log in comm_logs}

        # Batch fetch defaulters and create a lookup dictionary
        defaulters_map = {
            defaulter_id: defaulter.get("name")
            for defaulter_id, defaulter in zip(
                defaulter_ids, defaulters.get_defaulters_by_ids(defaulter_ids)
            )
            if defaulter
        }

        for comm_log in comm_logs:
            # Use batch-fetched defaulter data
            comm_log["name"] = defaulters_map.get(comm_log["defaulter_id"], "Unknown")

            # Use the commlog's own payment likelihood
            comm_log["payment_likelihood"] = comm_log.get("payment_likelihood", 0)

            comm_log.pop("issues", None)
            # comm_log.pop("id", None)

        return jsonify({"data": comm_logs}), 200

    elif request.method == "POST":
        data = request.get_json()
        if not data["id"]:
            # TODO: If posting WITHOUT an ID, then we are CREATING
            return (
                jsonify(
                    {
                        "error": "MissingAttributeError: A valid id is required to update this resource."
                    }
                ),
                400,
            )

        # TODO: Update the comm_log
        print(f"Updating comm log {data['id']} with {data}")
        feedback = data.get("feedback", None)

        update_comm_log(data["id"], {"feedback": feedback})
        return jsonify({"status": "success"}), 200


@communications_bp.route("/<comm_id>", methods=["GET", "OPTIONS"])
def get_communication(comm_id):
    # for production, assert UUID:
    # assert(len(defaulter_id) == 36)

    if request.method == "OPTIONS":
        return jsonify({}), 204

    if request.method == "GET":
        comm = get_comm_by_id(comm_id).data[0]
        summary = comm["summary"]
        if comm["channel"] == "email":
            email = get_emails_by_id([comm_id]).data[0]
            return jsonify({"transcript": email["email_body"], "summary": summary}), 200
        elif comm["channel"] == "call":
            # Check if this is a manual entry by trying to get call log
            try:
                calllog = get_calllog_by_id(comm_id)
                if calllog.data:
                    # This is an AI-generated call with call log
                    transcript = get_call_transcripts(
                        [{"comm_id": comm_id, "direction": "inbound"}]
                    )[0]
                    audio_recording_url = calllog.data.get("audio_recording_url", "")

                    return (
                        jsonify({
                            "transcript": transcript["transcript"],
                            "summary": summary,
                            "audio_recording_url": audio_recording_url,
                            "is_manual": False
                        }),
                        200,
                    )
                else:
                    # No call log found, this is a manual entry
                    return (
                        jsonify({
                            "transcript": summary,
                            "summary": summary,
                            "audio_recording_url": "",
                            "is_manual": True
                        }),
                        200,
                    )
            except Exception as e:
                print(f"Error fetching call log for {comm_id}: {e}")
                # If we can't fetch call log, assume it's manual
                return (
                    jsonify({
                        "transcript": summary,
                        "summary": summary,
                        "audio_recording_url": "",
                        "is_manual": True
                    }),
                    200,
                )

    return jsonify({}), 404


@communications_bp.route("/manual", methods=["POST", "OPTIONS"])
def create_manual_comm_log():
    """Create a manual communication log entry for human-to-human conversations"""
    if request.method == "OPTIONS":
        return jsonify({}), 204

    try:
        data = request.get_json()

        # Validate required fields
        required_fields = ["defaulter_id", "summary", "direction"]
        for field in required_fields:
            if not data.get(field):
                return jsonify({"error": f"Missing required field: {field}"}), 400

        # Validate direction
        if data["direction"] not in ["inbound", "outbound"]:
            return jsonify({"error": "Direction must be 'inbound' or 'outbound'"}), 400

        # Generate a unique comm_id for this manual entry (proper UUID format)
        manual_comm_id = str(uuid.uuid4())

        # Create timestamp
        timestamp = datetime.utcnow().isoformat() + 'Z'

        # Insert into commlogs table
        result = insert_comm_logs(
            defaulter_id=data["defaulter_id"],
            channel="call",  # Using 'call' channel for human conversations
            comm_id=manual_comm_id,
            direction=data["direction"],
            timestamp=timestamp,
            summary=data["summary"],
            payment_likelihood=data.get("payment_likelihood", None)
        )

        return jsonify({
            "status": "success",
            "message": "Manual communication log created successfully",
            "data": result.data[0] if result.data else None
        }), 201

    except Exception as e:
        print(f"Error creating manual comm log: {str(e)}")
        return jsonify({"error": "Failed to create manual communication log"}), 500


@communications_bp.route("/user/<defaulter_id>", methods=["GET", "OPTIONS"])
def get_comm_history_by_defaulter_id(defaulter_id):
    if request.method == "OPTIONS":
        return jsonify({}), 204

    comm_history = get_comm_logs_by_defaulter_id(defaulter_id).data

    emails = []
    calls = []

    for comm in comm_history:
        if comm["channel"] == "email":
            emails.append(comm)
        elif comm["channel"] == "call":
            calls.append(comm)

    email_ids = [email["comm_id"] for email in emails]

    emails = get_emails_by_id(email_ids).data
    call_transcripts = get_call_transcripts(calls)

    # add the transcripts and audio URLs to the calls
    for call, transcript in zip(calls, call_transcripts):
        call["transcript"] = transcript["transcript"]

        # Get call recording URL from calllogs table
        try:
            calllog = get_calllog_by_id(call["comm_id"])
            call["audio_recording_url"] = calllog.data.get("audio_recording_url") if calllog.data else None
        except Exception as e:
            print(f"Error fetching call recording URL for {call['comm_id']}: {e}")
            call["audio_recording_url"] = None

    for email in emails:
        email["transcript"] = email["email_body"]

    comm_history = calls + emails
    comm_history.sort(key=lambda x: x["timestamp"])

    return jsonify({"data": comm_history}), 200
